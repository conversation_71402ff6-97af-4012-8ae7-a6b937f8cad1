import {
  Project,
  UserRole,
  ProjectStatus,
  SalesWonSubStatus,
  SALES_STATUS_LABELS,
  SALES_WON_SUB_LABELS,
  User,
  getStatusesForRole,
  canModifyTask,
} from "@/types/project";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/StatusBadge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Eye, ArrowRight, DollarSign, Calendar, User as UserIcon, Phone } from "lucide-react";
import { formatDate, formatCurrency } from "@/lib/datetime";
import { useState } from "react";

interface SalesTableViewProps {
  projects: Project[];
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (projectId: string, newStatus: ProjectStatus, newSubStatus?: SalesWonSubStatus) => void;
  onViewDetails: (project: Project) => void;
  onAssignTask?: (projectId: string, assigneeId: string) => void;
  onFollowUp?: (projectId: string) => void;
  availableUsers?: User[];
  compact?: boolean; // New prop for ultra-compact mode
}

export const SalesTableView = ({
  projects,
  userRole,
  currentUserId,
  onStatusUpdate,
  onViewDetails,
  onAssignTask,
  onFollowUp,
  availableUsers = [],
  compact = false,
}: SalesTableViewProps) => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRowExpansion = (projectId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(projectId)) {
      newExpanded.delete(projectId);
    } else {
      newExpanded.add(projectId);
    }
    setExpandedRows(newExpanded);
  };

  const getNextStatus = (project: Project): { status: ProjectStatus; sub?: SalesWonSubStatus } | null => {
    // Handle won_deal sub-status progression
    if (project.status === "won_deal" && project.salesSubStatus) {
      const subStatuses: SalesWonSubStatus[] = ["10%", "5%", "45%", "37%", "3%"];
      const currentSubIndex = subStatuses.indexOf(project.salesSubStatus as SalesWonSubStatus);
      if (project.salesSubStatus === "3%") {
        return { status: "completed" };
      }
      if (currentSubIndex >= 0 && currentSubIndex < subStatuses.length - 1) {
        return { status: "won_deal", sub: subStatuses[currentSubIndex + 1] };
      }
    }

    // Regular status progression
    const salesStatuses: ProjectStatus[] = ["lead", "quotation", "potential", "won_deal", "lost_deal", "completed"];
    const currentIndex = salesStatuses.indexOf(project.status);
    if (project.status === "potential") {
      return { status: "won_deal", sub: "10%" };
    }
    if (currentIndex >= 0 && currentIndex < salesStatuses.length - 1) {
      return { status: salesStatuses[currentIndex + 1] };
    }
    return null;
  };

  const handleProgressClick = (project: Project) => {
    const next = getNextStatus(project);
    if (!next) return;
    if (next.status === "won_deal") {
      onStatusUpdate(project.id, "won_deal", next.sub);
      return;
    }
    onStatusUpdate(project.id, next.status);
  };

  const getAssignedUserName = (assignedTo: string | null) => {
    if (!assignedTo) return "Unassigned";
    const user = availableUsers.find(u => u.id === assignedTo);
    return user?.name || "Unknown User";
  };

  const handleStatusChange = (project: Project, newStatus: string) => {
    if (newStatus === "won_deal") {
      // Default to first sub-status when changing to won_deal
      onStatusUpdate(project.id, "won_deal", "10%");
    } else {
      onStatusUpdate(project.id, newStatus as ProjectStatus);
    }
  };

  const canUserModify = (project: Project) => {
    return canModifyTask(project, userRole, currentUserId);
  };

  return (
    <div className="rounded-md border">
      {/* Compact table for tight spaces */}
      <div className="overflow-x-auto">
        <Table className="text-xs table-fixed w-full" style={{ minWidth: '720px' }}>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[30px] p-1">No</TableHead>
              <TableHead className="w-[180px] p-1">Project</TableHead>
              <TableHead className="w-[60px] p-1">Date</TableHead>
              <TableHead className="w-[120px] p-1">Client</TableHead>
              <TableHead className="w-[45px] p-1">Src</TableHead>
              <TableHead className="w-[60px] p-1">VP</TableHead>
              <TableHead className="w-[50px] p-1">Landed</TableHead>
              <TableHead className="w-[60px] p-1">Amt</TableHead>
              <TableHead className="w-[80px] p-1">F-ups</TableHead>
              <TableHead className="text-right w-[70px] p-1">Actions</TableHead>
            </TableRow>
          </TableHeader>
        <TableBody>
          {projects.map((project, index) => {
            const next = getNextStatus(project);
            const isExpanded = expandedRows.has(project.id);

            return (
              <TableRow key={project.id} className="group">
                {/* No */}
                <TableCell className="text-center font-medium py-1 px-2 text-xs">
                  {index + 1}
                </TableCell>

                {/* Project with Remarks subtitle */}
                <TableCell className="font-medium py-1 px-2">
                  <div className="space-y-0.5">
                    <div className="font-semibold text-xs line-clamp-1 leading-tight" title={project.title}>
                      {project.title}
                    </div>
                    {project.remarks && (
                      <div className="text-xs text-muted-foreground line-clamp-1 leading-tight" title={project.remarks}>
                        {project.remarks}
                      </div>
                    )}
                  </div>
                </TableCell>

                {/* Date */}
                <TableCell className="py-1 px-2">
                  <div className="text-xs">
                    {project.projectDate ? formatDate(project.projectDate).split('/').slice(0,2).join('/') : '—'}
                  </div>
                </TableCell>
                {/* Client with Contact subtitle */}
                <TableCell className="py-1 px-2">
                  <div className="space-y-0.5">
                    <div className="text-xs font-medium truncate" title={project.client}>
                      {project.client}
                    </div>
                    {project.contact && (
                      <div className="text-xs text-muted-foreground truncate" title={project.contact}>
                        {project.contact}
                      </div>
                    )}
                  </div>
                </TableCell>

                {/* Source */}
                <TableCell className="py-1 px-2">
                  <div className="text-xs font-medium">
                    {project.source || '—'}
                  </div>
                </TableCell>

                {/* VP Date */}
                <TableCell className="py-1 px-2">
                  <div className="text-xs truncate">
                    {project.vpDate || '—'}
                  </div>
                </TableCell>

                {/* Landed */}
                <TableCell className="py-1 px-2">
                  <div className="text-center">
                    {project.landed ? (
                      <Badge variant="default" className="text-xs px-1 py-0">Y</Badge>
                    ) : (
                      <span className="text-xs text-muted-foreground">—</span>
                    )}
                  </div>
                </TableCell>


                {/* Sales Amount */}
                <TableCell className="py-1 px-2">
                  {project.salesAmount ? (
                    <div className="text-xs font-medium" title={formatCurrency(project.salesAmount)}>
                      {project.salesAmount >= 1000 ? `${Math.round(project.salesAmount/1000)}k` : project.salesAmount}
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-xs">—</span>
                  )}
                </TableCell>

                {/* Follow-ups - Compact 3 dates */}
                <TableCell className="py-1 px-2">
                  <div className="text-xs space-y-0.5">
                    <div className="font-medium text-muted-foreground">
                      ({project.followUpTimestamps?.length || 0}/3)
                    </div>
                    {Array.from({ length: 3 }).map((_, index) => {
                      const timestamp = project.followUpTimestamps?.[index];
                      return (
                        <div key={index} className="text-xs leading-tight">
                          <span className="font-medium">{index + 1}:</span>{' '}
                          {timestamp ? formatDate(timestamp).split('/').slice(0,2).join('/') : '—'}
                        </div>
                      );
                    })}
                  </div>
                </TableCell>


                <TableCell className="text-right py-1 px-2">
                  <div className="flex items-center justify-end gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewDetails(project)}
                      className="h-7 w-7 p-1"
                      title="View Details"
                    >
                      <Eye className="h-3 w-3" />
                    </Button>

                    {project.status === "lead" && onFollowUp && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onFollowUp(project.id)}
                        disabled={project.followUpTimestamps && project.followUpTimestamps.length >= 3}
                        className="h-7 w-7 p-1"
                        title={
                          project.followUpTimestamps && project.followUpTimestamps.length >= 3
                            ? "Maximum follow-ups reached"
                            : "Add follow-up"
                        }
                      >
                        <Phone className="h-3 w-3" />
                      </Button>
                    )}

                    {next && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleProgressClick(project)}
                        className="h-7 px-2 text-xs"
                        title={`Progress to ${next.status === "won_deal" && next.sub
                          ? SALES_WON_SUB_LABELS[next.sub]
                          : SALES_STATUS_LABELS[next.status as keyof typeof SALES_STATUS_LABELS]}`}
                      >
                        <ArrowRight className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
        </Table>
      </div>

      {projects.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          No sales projects found
        </div>
      )}
    </div>
  );
};
