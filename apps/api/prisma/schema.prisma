// Prisma schema for local Postgres and Supabase compatibility
// Use PostgreSQL with UUIDs for portability

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Project {
  id             String   @id @default(uuid()) @db.Uuid
  caseId         String   @db.Uuid // link tasks across roles
  parentTaskId   String?  @db.Uuid
  parentTask     Project? @relation("ParentChild", fields: [parentTaskId], references: [id])
  children       Project[] @relation("ParentChild")

  title          String
  client         String
  status         String   @default("sales_pending_assign") // mirrors Sales/Designer/Supervisor statuses
  salesAmount    Int      @default(0)
  assignedTo     String?  // user id
  salesSubStatus String?  // e.g. "10%"
  dueDate        DateTime?
  expiredDate    DateTime?
  revisions3d    String[] @default([])
  revisions2d    String[] @default([])
  isRevision     Boolean  @default(false)
  comment        String?  // project comments/notes (renamed from remarks)
  followUpTimestamps String[] @default([]) // timestamps for lead follow-ups (max 3)

  // New project creation fields
  contact        String?  // phone number
  projectDate    DateTime? // project date, defaults to today in form
  source         String?  // source: WS, FB, XHS, INSTA, Website
  vpDate         String?  // VP date as string
  landed         Boolean  @default(false) // landed toggle
  remarks        String?  // project creation remarks (different from comment)

  // Supervisor planning & progress
  supervisorSelectedPhases String[] @default([])
  supervisorPhaseDates     Json?
  supervisorPhaseStates    Json?

  // Supervisor project-level fields
  renovationPeriod         String?   // e.g., "3 months", "90 days"
  handoverDate            DateTime?
  supervisorProjectStartDate DateTime?

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([caseId, updatedAt])
  @@index([updatedAt])
  @@index([status])
  @@index([assignedTo])
  @@index([parentTaskId])
}

model TaskEvent {
  id          String   @id @default(uuid()) @db.Uuid
  caseId      String   @db.Uuid
  taskId      String   @db.Uuid
  role        String   // 'sales' | 'designer' | 'supervisor'
  type        String   // event type like 'task_created', 'status_changed', etc.
  occurredAt  DateTime @default(now())
  actorUserId String?  // who performed the action
  source      String   @default("api") // source of the event
  payload     Json?    // additional event data

  @@index([caseId, occurredAt])
  @@index([taskId, occurredAt])
  @@index([role, occurredAt])
}

model User {
  id          String   @id
  clerkUserId String   @unique
  name        String
  email       String   @unique
  role        String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([clerkUserId])
  @@index([email])
}

// Supabase uses Postgres, so this schema is compatible.

